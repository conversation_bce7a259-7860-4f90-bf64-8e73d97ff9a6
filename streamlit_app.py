import streamlit as st
from main import run_agent
import os
import tempfile
import shutil
import time

st.title("Solar Image PDF Generator")
st.write("Upload a ZIP or PDF file containing images (JPG, JPEG, PNG) or PDFs with embedded images.")

uploaded_file = st.file_uploader("Upload ZIP or PDF", type=['zip', 'pdf'])

if uploaded_file:
    # Create a temporary file to save the uploaded content
    with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(uploaded_file.name)[1]) as temp_input:
        temp_input.write(uploaded_file.read())
        temp_input_path = temp_input.name

    try:
        st.info(f"Processing '{uploaded_file.name}'...")
        
        progress_text = st.empty()
        progress_bar = st.progress(0)

        output_pdf_name = "categorized_images.pdf"
        # The output PDF will be generated in the temporary directory managed by run_agent
        # We just need a path for run_agent to know where to save it.
        # For Streamlit, it's better to let run_agent handle its own temp_dir for extraction
        # and then we just provide a target output path.
        # Let's create a temporary directory for the output PDF specifically for Streamlit's use.
        output_dir_for_streamlit = tempfile.mkdtemp()
        output_pdf_path = os.path.join(output_dir_for_streamlit, output_pdf_name)

        # Add a slider or number input for chunk size (optional, default 10)
        chunk_size = st.number_input("Images per API batch (chunk size)", min_value=1, max_value=20, value=20, step=1, help="Number of images to send to the LLM at once.")

        progress_text.text("Categorizing images and generating PDF...")
        progress_bar.progress(50) # Indicate some progress

        start_time = time.time()
        generated_pdf_path, total_input_tokens, total_output_tokens, chunk_times = run_agent(temp_input_path, output_pdf_path, chunk_size=chunk_size)
        end_time = time.time()
        elapsed_seconds = end_time - start_time
        elapsed_minutes = int(elapsed_seconds // 60)
        elapsed_seconds_remainder = int(elapsed_seconds % 60)

        progress_bar.progress(100) # Complete the progress bar
        progress_text.empty() # Clear the progress text

        if generated_pdf_path and os.path.exists(generated_pdf_path):
            # Calculate estimated cost
            cost_vision_input = (total_input_tokens / 1_000_000) * 0.10 # $0.10 per 1M vision tokens
            cost_text_output = (total_output_tokens / 1_000_000) * 0.40 # $1.40 per 1M text tokens
            total_estimated_cost = cost_vision_input + cost_text_output

            st.success("PDF generated successfully!")
            st.write(f"**Estimated Gemini API Cost:** ${total_estimated_cost:.6f}")
            st.write(f"*(Input Tokens: {total_input_tokens}, Output Tokens: {total_output_tokens})*")
            st.write(f"**Total Processing Time:** {elapsed_minutes} min {elapsed_seconds_remainder} sec")
            
            # Display chunk timing information
            if chunk_times:
                st.write("**Chunk Processing Times:**")
                for i, chunk_time in enumerate(chunk_times):
                    chunk_minutes = int(chunk_time // 60)
                    chunk_seconds = int(chunk_time % 60)
                    if chunk_minutes > 0:
                        st.write(f"  Chunk {i+1}: {chunk_minutes} min {chunk_seconds} sec")
                    else:
                        st.write(f"  Chunk {i+1}: {chunk_seconds} sec")
                
                # Calculate average chunk time
                avg_chunk_time = sum(chunk_times) / len(chunk_times)
                avg_minutes = int(avg_chunk_time // 60)
                avg_seconds = int(avg_chunk_time % 60)
                if avg_minutes > 0:
                    st.write(f"**Average Chunk Time:** {avg_minutes} min {avg_seconds} sec")
                else:
                    st.write(f"**Average Chunk Time:** {avg_seconds} sec")
            
            with open(generated_pdf_path, "rb") as f:
                st.download_button(
                    label="Download PDF",
                    data=f.read(),
                    file_name="categorized_images.pdf",
                    mime="application/pdf"
                )
        else:
            st.error("Failed to generate PDF. This might be due to no images found/categorized, or an API issue.")
            st.info("Please check the terminal for more detailed error messages (e.g., API key issues, image processing errors).")
            # If no PDF generated, still show token count if available (e.g., API key issue after some calls)
            if total_input_tokens > 0 or total_output_tokens > 0:
                st.write(f"*(Attempted API calls: Input Tokens: {total_input_tokens}, Output Tokens: {total_output_tokens})*")

    except Exception as e:
        st.error(f"An error occurred during processing: {e}")
        st.info("Please check the terminal for more detailed error messages.")
    finally:
        # Clean up the temporary input file and the temporary output directory
        if os.path.exists(temp_input_path):
            os.unlink(temp_input_path)
            print(f"DEBUG: Cleaned up temporary input file: {temp_input_path}")
        if os.path.exists(output_dir_for_streamlit):
            shutil.rmtree(output_dir_for_streamlit)
            print(f"DEBUG: Cleaned up temporary output directory: {output_dir_for_streamlit}")
