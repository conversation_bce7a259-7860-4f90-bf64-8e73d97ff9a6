# Image Categorization and PDF Generation API

This project provides a Flask API that processes ZIP archives or PDF documents containing images, categorizes these images using the Gemini Vision Language Model, and generates a PDF report with the categorized images.

## Description

The application is designed to automate the process of organizing and documenting images, particularly useful for projects involving visual data that needs to be categorized and presented in a structured format. It leverages Google's Gemini API for intelligent image categorization.

## Features

*   **Image Extraction**: Extracts images from uploaded ZIP archives and PDF documents.
*   **AI-Powered Categorization**: Utilizes the Gemini Vision Language Model to categorize images based on their content.
*   **PDF Report Generation**: Creates a well-structured PDF document, grouping images by their assigned categories and including their original filenames as captions.
*   **Flask API**: Provides a simple RESTful API endpoint for easy integration with other applications.
*   **Error Handling**: Includes robust error handling for file uploads, API calls, and PDF generation.
*   **Temporary File Management**: Manages temporary files and directories for secure and clean processing.

## Setup and Installation

To set up and run this project locally, follow these steps:

### Prerequisites

*   Python 3.8+
*   `pip` (Python package installer)

### 1. Clone the Repository

```bash
git clone https://github.com/Ayushwattmonk/PDF.git
cd PDF
```

### 2. Create a Virtual Environment (Recommended)

```bash
python -m venv venv
# On Windows
.\venv\Scripts\activate
# On macOS/Linux
source venv/bin/activate
```

### 3. Install Dependencies

Install the required Python packages using `pip`:

```bash
pip install -r requirements.txt
```
*(Note: A `requirements.txt` file is assumed to exist or will need to be created with `Flask`, `python-dotenv`, `Pillow`, `requests`, `reportlab`, `PyMuPDF`, `numpy`.)*

### 4. Set Up Gemini API Key

Obtain a `GEMINI_API_KEY` from the Google AI Studio (https://aistudio.google.com/app/apikey).

Create a `.env` file in the root directory of the project and add your API key:

```
GEMINI_API_KEY=YOUR_ACTUAL_GEMINI_API_KEY
```

### 5. Run the Flask Application

```bash
python app.py
```

The application will start on `http://127.0.0.1:5000/`.

## Usage

The API exposes a single endpoint for processing files.

### Endpoint: `/process_zip`

*   **URL**: `http://127.0.0.1:5000/process_zip`
*   **Method**: `POST`
*   **Content-Type**: `multipart/form-data`

#### Request Body

The request should contain a file uploaded under the field name `file`. The file can be either a `.zip` archive or a `.pdf` document.

Example using `curl`:

**For a ZIP file:**
```bash
curl -X POST -F "file=@/path/to/your/images.zip" http://127.0.0.1:5000/process_zip -o categorized_images.pdf
```

**For a PDF file:**
```bash
curl -X POST -F "file=@/path/to/your/document.pdf" http://127.0.0.1:5000/process_zip -o categorized_images.pdf
```

#### Responses

*   **Success (200 OK)**: Returns the generated PDF file as an attachment.
*   **Client Error (400 Bad Request)**: If no file is provided, no file is selected, or an invalid file type is uploaded.
*   **Server Error (500 Internal Server Error)**: If an unexpected error occurs during processing (e.g., API key not set, issues with image categorization, PDF generation failure).

## File Structure

*   `app.py`: The main Flask application, handling API routes and file uploads.
*   `main.py`: Contains the core logic for image extraction, categorization using the Gemini API, and PDF report generation.
*   `.env`: Stores environment variables, including the `GEMINI_API_KEY`.
*   `streamlit_app.py`: (If applicable) A Streamlit interface for interacting with the API.
*   `requirements.txt`: Lists all Python dependencies.
