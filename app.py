import os
from flask import Flask, request, send_file, jsonify
from main import run_agent
import tempfile
import shutil
from dotenv import load_dotenv # Import load_dotenv

# Load environment variables from .env file at the application's entry point
load_dotenv()

app = Flask(__name__)

@app.route('/process_zip', methods=['POST'])
def process_zip_file():
    if 'file' not in request.files:
        return jsonify({"error": "No file part in the request"}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({"error": "No selected file"}), 400
    
    print(f"DEBUG: Uploaded file filename: {file.filename}")
    print(f"DEBUG: Uploaded file mimetype: {file.mimetype}")

    if file and (file.filename.lower().endswith('.zip') or file.filename.lower().endswith('.pdf')): # Use .lower() for case-insensitivity
        temp_dir = None
        try:
            # Save the uploaded zip file to a temporary location
            temp_dir = tempfile.mkdtemp()
            zip_path = os.path.join(temp_dir, file.filename)
            file.save(zip_path)

            output_pdf_name = "categorized_images.pdf"
            output_pdf_path = os.path.join(temp_dir, output_pdf_name)

            # Run the agent
            generated_pdf_path, input_tokens, output_tokens, chunk_times = run_agent(zip_path, output_pdf_path)

            if generated_pdf_path and os.path.exists(generated_pdf_path):
                # Send the generated PDF back
                print(f"Attempting to send file from: {generated_pdf_path}")
                
                response = send_file(generated_pdf_path, as_attachment=True, download_name=output_pdf_name, mimetype='application/pdf')
                
                # Schedule cleanup of the temporary directory after the response is sent
                @response.call_on_close
                def cleanup_temp_dir():
                    if temp_dir and os.path.exists(temp_dir):
                        print(f"DEBUG: Cleaning up temporary directory after response sent: {temp_dir}")
                        shutil.rmtree(temp_dir, ignore_errors=True) # Use ignore_errors for robustness
                    else:
                        print(f"DEBUG: No temporary directory to clean up or it was already removed: {temp_dir}")
                return response
            else:
                # If run_agent returned None, it means no PDF was generated (e.g., no images found/categorized)
                print("INFO: run_agent did not generate a PDF (likely no images found or categorized).")
                # Ensure temp_dir is cleaned up immediately if no PDF is generated
                if temp_dir and os.path.exists(temp_dir):
                    print(f"DEBUG: Cleaning up temporary directory immediately (no PDF generated): {temp_dir}")
                    shutil.rmtree(temp_dir, ignore_errors=True)
                return jsonify({"error": "No images found in the ZIP file or no images could be categorized to generate a PDF."}), 400

        except Exception as e:
            print(f"Caught general Exception: {e}")
            return jsonify({"error": str(e)}), 500
        finally:
            # The main cleanup is now handled by @response.call_on_close or immediately if no PDF.
            # This outer finally block is mostly for unhandled exceptions before send_file.
            pass # No action needed here, cleanup is handled elsewhere
    else:
        return jsonify({"error": "Invalid file type. Please upload a ZIP file."}), 400

if __name__ == '__main__':
    # Ensure GEMINI_API_KEY is set in your environment
    if not os.getenv("GEMINI_API_KEY"):
        print("WARNING: GEMINI_API_KEY environment variable is not set.")
        print("Please set it before running the Flask app.")
        print("Example (Windows): set GEMINI_API_KEY=YOUR_API_KEY_HERE")
        print("Example (Linux/macOS): export GEMINI_API_KEY=YOUR_API_KEY_HERE")
        # For local testing, you might uncomment the following line, but not for production
        # os.environ["GEMINI_API_KEY"] = "YOUR_DUMMY_API_KEY_FOR_TESTING" 

    app.run(debug=True, port=5000)
