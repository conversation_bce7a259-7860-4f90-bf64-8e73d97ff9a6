import zipfile
import os
import tempfile
from PIL import Image
import glob
import base64
import requests
from collections import defaultdict
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image as RLImage, PageBreak, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib.pagesizes import letter
from reportlab.lib.units import inch
import shutil
from dotenv import load_dotenv
import fitz # PyMuPDF
import concurrent.futures # Import for parallel processing
import time # Import for timing

# Load environment variables from .env file
load_dotenv()

def extract_images_from_pdf(pdf_path, output_dir):
    """
    Extracts images from a PDF file and saves them to the output_dir.
    Returns a list of paths to the extracted images.
    """
    extracted_image_paths = []
    try:
        doc = fitz.open(pdf_path)
        for i in range(len(doc)):
            for img_index, img_info in enumerate(doc.get_page_images(i)):
                xref = img_info[0]
                base_image = doc.extract_image(xref)
                image_bytes = base_image["image"]
                image_ext = base_image["ext"]
                
                # Construct a unique filename for the extracted image
                image_filename = f"page{i+1}_img{img_index+1}.{image_ext}"
                image_path = os.path.join(output_dir, image_filename)
                
                with open(image_path, "wb") as img_file:
                    img_file.write(image_bytes)
                extracted_image_paths.append(image_path)
        doc.close()
        print(f"DEBUG: Extracted {len(extracted_image_paths)} images from PDF: {pdf_path}")
    except Exception as e:
        print(f"ERROR: Could not extract images from PDF {pdf_path}: {e}")
    return extracted_image_paths

def extract_images(zip_path):
    temp_dir = tempfile.mkdtemp()
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        zip_ref.extractall(temp_dir)
    return temp_dir

def find_images(directory):
    valid_image_ext = ['.jpg', '.jpeg', '.png']
    valid_pdf_ext = ['.pdf']
    image_paths = []
    
    for root, _, files in os.walk(directory):
        for f in files:
            file_path = os.path.join(root, f)
            file_ext = os.path.splitext(f)[1].lower()

            if file_ext in valid_image_ext:
                image_paths.append(file_path)
            elif file_ext in valid_pdf_ext:
                # If it's a PDF, extract images from it
                print(f"DEBUG: Found PDF file: {file_path}. Attempting to extract images.")
                extracted_from_pdf = extract_images_from_pdf(file_path, directory) # Save extracted images in the same temp_dir
                image_paths.extend(extracted_from_pdf)
    
    print(f"DEBUG: Total image paths found (including extracted from PDFs): {len(image_paths)}")
    return image_paths

def get_image_category(image_path, optimized_images_dir, max_image_size=(1024, 1024), jpeg_quality=85):
    """
    Categorizes an image using the Gemini API after optimizing its size.
    Saves the optimized image to a temporary file and returns its path along with category and token counts.
    """
    optimized_image_path = None
    try:
        # Open the image using PIL
        img = Image.open(image_path)
        
        # Convert to RGB if not already (important for JPEG saving)
        if img.mode != 'RGB':
            img = img.convert('RGB')

        # Resize image if it exceeds max_image_size
        img.thumbnail(max_image_size, Image.Resampling.LANCZOS)

        # Save the optimized image to a BytesIO object
        import io
        img_byte_arr = io.BytesIO()
        img.save(img_byte_arr, format='JPEG', quality=jpeg_quality)
        img_byte_arr = img_byte_arr.getvalue()
        
        b64_image = base64.b64encode(img_byte_arr).decode('utf-8')

        # Save the optimized image to a temporary file for later PDF generation
        optimized_image_filename = f"optimized_{os.path.basename(image_path).replace('.', '_')}.jpeg"
        optimized_image_path = os.path.join(optimized_images_dir, optimized_image_filename)
        with open(optimized_image_path, "wb") as f:
            f.write(img_byte_arr)
        print(f"DEBUG: Saved optimized image to: {optimized_image_path}")

    except Exception as e:
        print(f"ERROR: Could not optimize image {image_path}: {e}")
        # Fallback to original image if optimization fails, and don't save optimized version
        with open(image_path, "rb") as img_file:
            b64_image = base64.b64encode(img_file.read()).decode('utf-8')
        optimized_image_path = image_path # Use original path if optimization fails
    
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("ERROR: GEMINI_API_KEY environment variable is not set.")
        raise ValueError("GEMINI_API_KEY environment variable is not set. Please set it to your actual Gemini API key.")
    else:
        print(f"DEBUG: GEMINI_API_KEY loaded (first 5 chars): {api_key[:5]}*****")

    prompt = """**Analyze the provided image thoroughly to determine the most appropriate category from the list below.**

You are a **Vision Language Model** tasked with **categorizing images related to solar installation projects**. 
Your goal is to **accurately assign the image to one of the specified categories** by deeply analyzing its content, 
following the guidelines provided.
Objective: Categorize the given image into one of the predefined solar installation categories based on its visual content and identifiable elements.
Categories and Classification Criteria:

1.**Address**:
Identify if the image contains the front of a house with a clearly visible street address.
If no house number is visible, cross-reference with satellite imagery to verify the home’s identity.
The address may be combined with other images but should primarily serve as verification.

2.**Overview**:
Detect zoomed-out images of a wall with solar-related equipment installed.
Equipment may include monitoring devices, disconnect switches, and conduit, MSP, Utility Meter.
Zoomed-out Images of Wall which contain the Electrical Equipment, Conduits , MSP , Meters etc.
Image contain the complete overview of electrical equipment presented.
Utility Meter, MSP etc. 

3.**DC_Disconnect**: 
Identify images showing an external DC disconnect installed on a wall with conduit visible.
Since external DC disconnects are uncommon, ensure proper labeling and verification.
To identify a DC disconnect switch. Key features include:
Labeling: Look for labels such as 'DC Disconnect' or 'PV Disconnect'.
Switch Mechanism: A handle or switch indicating 'ON' and 'OFF' positions.
Internal Components: Rotary knife contacts or spring-loaded mechanisms designed for rapid disconnection.
If these features are present, classify the image under the 'DC Disconnect' category."
Clear image of DC disconnect Only if confirm its a DC disconnect.


4.**Module_label**: 
A zoomed-in photo clearly showing manufacturer, model, and serial number.
Clear Zoomed images of label of solar module showing clear module and serial number.
In this do not classify the images of optimizers or microinverter labels , Only the Labels of Solar Modules clearly stating the branch name serial number and all details.

5.**Inverter_FrontCover**: 
Image of inverter of front cover showing the internal wiring of inverter. Image of solar inverter focusing on wiring of inverter.

6.**Inverter_label**: 
Inverter’s model and serial number should be visible.
Image of Labels of inverter of inverter showing clearly the model/serial number and ratings. 

7.**AC_Disconnect**:
Identify images showing an AC disconnect, if installed.
Ensure the image clearly captures the AC disconnect and its relevant components.
Clear image of AC disconnect Only if confirm its a AC disconnect.

8.**PV_Installation**
Detect images showing installed solar modules from the roof.
The photo should capture all installed modules from a close-up vantage point.
Confirm mechanical connections, proper aesthetics, and the total number of panels installed in each array.
Images of solar modules and panels and the installation images.

9. **Battery**:
Identify images containing energy storage devices.
Ensure the photo includes model and serial numbers of all batteries and gateway components.
clear images of battery Close Images of Battery and the labels of battery 
Clear zoomed single Label of battery with visible Serial /Model number and rating.

10.**Permit**:
Detect images showing official permit proof.
This may include a photo or screenshot of the AHJ-issued permit or job card.
Documentation which is a permit image of the permit document and screenshots.

11.**Optimizer_Microinverter**:
Classify the images of optimizers and microinverter they are small box typically in black color they are attached below or near solar panels
Microinverters are small inverters installed on each solar panel, converting the DC electricity produced by each panel into AC electricity individually.Microinverters are generally small, flat, and rectangular
Optimizers are devices installed on each solar panel to optimize its performance individually. They track the maximum power point (MPPT) for each panel, ensuring each one operates at its highest efficiency.
They often have a small, rectangular shape, with a few electrical connections (wires) for input and output. They are usually black or metallic in colo

Note: 
To Classify between AC or DC disconnect switch. Key identifiers include:
Labeling: 'AC Disconnect' or 'Electrical Disconnect' indicates an AC disconnect; 'DC Disconnect' or 'PV Disconnect' signifies a DC disconnect.
Switch Mechanism: Both types feature a handle or switch with 'ON' and 'OFF' positions.
Enclosure: Typically, both are rectangular or square boxes made of metal or durable plastic.
If the image lacks clear labeling, consider the context and location within the electrical system to aid classification

Instructions:
Analyze the image and extract identifiable text and features.
Match the image content against the defined categories based on visual elements and any available metadata.
If an image fits multiple categories, prioritize categorization based on the most dominant features.
If an image does not fit any category, flag it as "Uncategorized" for manual review. and mark it as **Uncategorized**
### **Response Format**:
Provide the category in the following format:
Category: [Category Name]
Do not include any additional text, explanations, or reasoning in your response.
Your objective is to deliver a single, accurate category for the image based on a deep analysis of its content.
"""
    
    payload = {
        "contents": [{
            "parts": [
                {"text": prompt},
                {"inline_data": {
                    "mime_type": "image/jpeg", # Assuming all images are processed as JPEG for Gemini
                    "data": b64_image
                }}
            ]
        }]
    }

    headers = {
        "Content-Type": "application/json"
    }

    response = requests.post(f"https://generativelanguage.googleapis.com/v1/models/gemini-2.0-flash:generateContent?key={api_key}", 
                             json=payload, headers=headers)
    
    # Print status code and response text for debugging
    print(f"DEBUG: Gemini API Response Status Code: {response.status_code}")
    print(f"DEBUG: Gemini API Response Text: {response.text}")

    response.raise_for_status() # Raise an exception for HTTP errors (e.g., 400, 401, 403, 500)
    
    try:
        category = response.json()['candidates'][0]['content']['parts'][0]['text'].strip()
        # Extract token counts from the response
        input_tokens = response.json().get('usageMetadata', {}).get('promptTokenCount', 0)
        output_tokens = response.json().get('usageMetadata', {}).get('candidatesTokenCount', 0)
        return category.replace("Category: ", ""), input_tokens, output_tokens, optimized_image_path  # clean it
    except (KeyError, IndexError) as e:
        print(f"Error parsing Gemini response for image {image_path}: {e}")
        print(f"Full response: {response.json()}")
        return "Other", 0, 0, optimized_image_path # Default to 'Other' if category cannot be parsed

def chunk_list(data, chunk_size):
    """Yield successive chunk_size-sized chunks from data."""
    for i in range(0, len(data), chunk_size):
        yield data[i:i + chunk_size]

def group_images_by_category(image_paths, optimized_images_dir, chunk_size=10):
    grouped = defaultdict(list)
    total_input_tokens = 0
    total_output_tokens = 0
    chunk_times = []
    
    # Calculate total number of chunks once
    total_chunks = (len(image_paths) + chunk_size - 1) // chunk_size
    
    for chunk_idx, chunk in enumerate(chunk_list(image_paths, chunk_size)):
        print(f"Processing chunk {chunk_idx + 1} of {total_chunks} ({len(chunk)} images)")
        chunk_start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=20) as executor:
            # Pass optimization parameters to get_image_category
            future_to_path = {executor.submit(get_image_category, path, optimized_images_dir, max_image_size=(1024, 1024), jpeg_quality=85): path for path in chunk}
            for future in concurrent.futures.as_completed(future_to_path):
                path = future_to_path[future]
                try:
                    category, input_t, output_t, optimized_path = future.result()
                    if optimized_path: # Use the optimized path for grouping
                        grouped[category].append(optimized_path)
                    else: # Fallback to original path if optimization failed
                        grouped[category].append(path)
                    total_input_tokens += input_t
                    total_output_tokens += output_t
                except Exception as e:
                    print(f"Could not categorize image {path}: {e}")
                    grouped["Error_Categorizing"].append(path)
        
        chunk_end_time = time.time()
        chunk_elapsed = chunk_end_time - chunk_start_time
        chunk_times.append(chunk_elapsed)
        print(f"Chunk {chunk_idx + 1} completed in {chunk_elapsed:.2f} seconds")
    
    return grouped, total_input_tokens, total_output_tokens, chunk_times

def generate_pdf(grouped_images, output_pdf):
    doc = SimpleDocTemplate(output_pdf, pagesize=letter)
    styles = getSampleStyleSheet()
    story = []

    MAX_COLS = 3  # Max number of images per row
    # Define maximum dimensions for images in points
    # Letter page is 8.5 x 11 inches. Default margins are 1 inch on each side.
    # Usable width = (8.5 - 2*1) * inch = 6.5 * inch = 468 points.
    # Usable height = (11 - 2*1) * inch = 9 * inch = 648 points.
    
    # Calculate max width per column, leaving some horizontal padding between images
    # Let's aim for 10 points padding on each side of the image within its cell (total 20 points per image for padding)
    HORIZONTAL_PADDING_PER_IMAGE_SIDE = 10 # points
    MAX_COL_WIDTH_PTS = (letter[0] - 2 * inch) / MAX_COLS - (2 * HORIZONTAL_PADDING_PER_IMAGE_SIDE)
    
    # Max height for an image, leaving space for caption and vertical spacing
    IMAGE_MAX_HEIGHT_PTS = 140 # points (approx 1.94 inches)

    # Ensure IMAGE_MAX_WIDTH_PTS is not too large, cap it at a reasonable value if calculation yields more
    IMAGE_MAX_WIDTH_PTS = min(MAX_COL_WIDTH_PTS, 140) 

    # Define a custom style for image captions (filenames)
    caption_style = styles['Normal']
    caption_style.alignment = 1 # Center alignment

    for idx, (category, images) in enumerate(grouped_images.items()):
        story.append(Paragraph(f"<b>{category}</b>", styles['Title']))
        story.append(Spacer(1, 0.2 * inch))

        # Sort images by filename for consistent output
        images.sort(key=os.path.basename)

        rows_of_cells = []
        current_row_cells = []

        for image_path in images:
            try:
                # Open image with PIL to get its dimensions
                pil_img = Image.open(image_path)
                original_width_px, original_height_px = pil_img.size
                pil_img.close() # Close the PIL image to free up resources

                print(f"DEBUG: Processing image: {os.path.basename(image_path)}")
                print(f"DEBUG: Original dimensions: {original_width_px}x{original_height_px} px")

                # Calculate aspect ratio
                aspect_ratio = original_width_px / original_height_px

                # Calculate new dimensions in points, maintaining aspect ratio
                new_width_pts = IMAGE_MAX_WIDTH_PTS
                new_height_pts = new_width_pts / aspect_ratio

                # If new height exceeds max height, scale based on height instead
                if new_height_pts > IMAGE_MAX_HEIGHT_PTS:
                    new_height_pts = IMAGE_MAX_HEIGHT_PTS
                    new_width_pts = new_height_pts * aspect_ratio
                
                print(f"DEBUG: Scaled dimensions: {new_width_pts:.2f}x{new_height_pts:.2f} pts")

                img = RLImage(image_path, width=new_width_pts, height=new_height_pts)
                caption = Paragraph(os.path.basename(image_path), caption_style)
                
                # Create a cell containing the image and its caption
                # Use a nested Table for the cell content to ensure image and caption are vertically stacked
                cell_content = Table([[img], [caption]], colWidths=[new_width_pts]) # Use new_width_pts for cell colWidth
                cell_content.setStyle(TableStyle([
                    ('ALIGN', (0,0), (-1,-1), 'CENTER'),
                    ('VALIGN', (0,0), (-1,-1), 'TOP'),
                    ('BOTTOMPADDING', (0,0), (-1,-1), 10), # Increased padding below caption
                    ('LEFTPADDING', (0,0), (-1,-1), HORIZONTAL_PADDING_PER_IMAGE_SIDE), # Horizontal padding
                    ('RIGHTPADDING', (0,0), (-1,-1), HORIZONTAL_PADDING_PER_IMAGE_SIDE), # Horizontal padding
                ]))
                current_row_cells.append(cell_content)

                if len(current_row_cells) == MAX_COLS:
                    rows_of_cells.append(current_row_cells)
                    current_row_cells = []

            except Exception as e:
                print(f"Could not add image {image_path} to PDF: {e}")
                error_caption = Paragraph(f"Error loading image: {os.path.basename(image_path)}", styles['Normal'])
                # Use IMAGE_MAX_WIDTH_PTS for error cell width
                error_cell_content = Table([[error_caption]], colWidths=[IMAGE_MAX_WIDTH_PTS])
                error_cell_content.setStyle(TableStyle([
                    ('ALIGN', (0,0), (-1,-1), 'CENTER'),
                    ('VALIGN', (0,0), (-1,-1), 'MIDDLE'),
                    ('BOTTOMPADDING', (0,0), (-1,-1), 10), # Increased padding
                    ('LEFTPADDING', (0,0), (-1,-1), HORIZONTAL_PADDING_PER_IMAGE_SIDE), # Horizontal padding
                    ('RIGHTPADDING', (0,0), (-1,-1), HORIZONTAL_PADDING_PER_IMAGE_SIDE), # Horizontal padding
                ]))
                current_row_cells.append(error_cell_content)
                if len(current_row_cells) == MAX_COLS:
                    rows_of_cells.append(current_row_cells)
                    current_row_cells = []

        # Add any remaining cells as the last row
        if current_row_cells:
            rows_of_cells.append(current_row_cells)

        # Create a main table for the grid layout
        if rows_of_cells:
            # Calculate column widths for the main table. Each column will have the same width.
            # Use MAX_COL_WIDTH_PTS for the main table's column widths
            col_widths = [MAX_COL_WIDTH_PTS + (2 * HORIZONTAL_PADDING_PER_IMAGE_SIDE)] * MAX_COLS # Account for inner cell padding
            
            grid_table = Table(rows_of_cells, colWidths=col_widths)
            grid_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 0),
                ('TOPPADDING', (0, 0), (-1, -1), 0),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 24), # Increased space between rows
            ]))
            story.append(grid_table)
            story.append(Spacer(1, 0.5 * inch))

        # Add a page break after each category, except the last one
        if idx < len(grouped_images) - 1:
            story.append(PageBreak())
    
    try:
        print(f"Attempting to build PDF at: {output_pdf}")
        doc.build(story)
        print(f"PDF build process completed for: {output_pdf}")
        if not os.path.exists(output_pdf):
            print(f"WARNING: PDF file was not found at {output_pdf} after build process.")
            raise FileNotFoundError(f"PDF file was not created at {output_pdf}")
        return output_pdf  # Return the path to the generated PDF
    except Exception as e:
        print(f"CRITICAL ERROR: Failed to build PDF at {output_pdf}: {e}")
        raise # Re-raise the exception to be caught by the caller

def cleanup(temp_dir):
    shutil.rmtree(temp_dir)

def run_agent(file_path, output_pdf, chunk_size=20):
    # Create a temporary working directory
    temp_dir = tempfile.mkdtemp()
    optimized_images_dir = tempfile.mkdtemp() # New temporary directory for optimized images
    print(f"Working directory: {temp_dir}")
    print(f"Optimized images directory: {optimized_images_dir}")

    total_input_tokens = 0
    total_output_tokens = 0
    chunk_times = []
    generated_pdf_path = None # Initialize to None

    try:
        file_ext = os.path.splitext(file_path)[1].lower()

        if file_ext == '.zip':
            print(f"Detected ZIP file. Extracting: {file_path}")
            with zipfile.ZipFile(file_path, 'r') as zip_ref:
                zip_ref.extractall(temp_dir)
        elif file_ext == '.pdf':
            print(f"Detected PDF file. Extracting images: {file_path}")
            extracted_images = extract_images_from_pdf(file_path, temp_dir)
            if not extracted_images:
                print("No images extracted from the PDF.")
                return None, 0, 0, []
        else:
            print("ERROR: Unsupported file type. Please upload a ZIP or PDF file.")
            return None, 0, 0, []

        # Whether it was a zip or pdf, find all images now in temp_dir
        image_paths = find_images(temp_dir)
        print(f"Found {len(image_paths)} images.")

        if not image_paths:
            print("No valid images found. Skipping PDF generation.")
            return None, 0, 0, []

        print("Categorizing images using Gemini API...")
        grouped_images_dict, total_input_tokens, total_output_tokens, chunk_times = group_images_by_category(image_paths, optimized_images_dir, chunk_size=chunk_size)

        if not grouped_images_dict:
            print("Categorization failed or returned no results.")
            return None, total_input_tokens, total_output_tokens, chunk_times

        print("Generating categorized PDF...")
        generated_pdf_path = generate_pdf(grouped_images_dict, output_pdf)
        print(f"PDF generated: {generated_pdf_path}")
        return generated_pdf_path, total_input_tokens, total_output_tokens, chunk_times

    except Exception as e:
        print(f"Exception in run_agent: {e}")
        return None, total_input_tokens, total_output_tokens, chunk_times
    finally:
        print("Cleaning up temporary files.")
        shutil.rmtree(temp_dir, ignore_errors=True)
        shutil.rmtree(optimized_images_dir, ignore_errors=True) # Clean up optimized images directory

if __name__ == "__main__":
    # Example usage:
    # To run this, you would typically call run_agent from another script
    # or modify this __main__ block to accept command line arguments for zip_path and output_pdf.
    # For demonstration, let's assume a dummy zip file and output path.
    print("This script is designed to be run via the `run_agent` function.")
    print("Please ensure you have a 'GEMINI_API_KEY' environment variable set.")
    print("Example: os.environ['GEMINI_API_KEY'] = 'YOUR_API_KEY'")
    print("Then call run_agent('path/to/your/zipfile.zip', 'output.pdf')")

    # Example of how you might run it if you had a test.zip
    # from dotenv import load_dotenv
    # load_dotenv() # Load environment variables from .env file if it exists
    #
    # test_zip_path = "test_images.zip" # Replace with a path to a dummy zip file for testing
    # test_output_pdf = "categorized_images.pdf"
    #
    # # Create a dummy zip file for testing if it doesn't exist
    # if not os.path.exists(test_zip_path):
    #     print(f"Creating a dummy zip file '{test_zip_path}' for testing...")
    #     os.makedirs("temp_test_images", exist_ok=True)
    #     with open("temp_test_images/image1.jpg", "w") as f: f.write("dummy image content")
    #     with open("temp_test_images/image2.png", "w") as f: f.write("dummy image content")
    #     with zipfile.ZipFile(test_zip_path, 'w') as zf:
    #         zf.write("temp_test_images/image1.jpg", "image1.jpg")
    #         zf.write("temp_test_images/image2.png", "image2.png")
    #     shutil.rmtree("temp_test_images")
    #     print("Dummy zip file created. You can now run the agent.")
    #
    # # Uncomment the line below to run the agent with the dummy zip
    # # run_agent(test_zip_path, test_output_pdf)
